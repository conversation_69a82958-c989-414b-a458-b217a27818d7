const React = require('react');

function Fieldset({ fields }) {

  const entries = Object.entries(fields);

  return (
    <div className="cfp-results-fieldset">
      {entries.map(([field, value]) => {

        return (
          <div 
            key={field}
            className="cfp-results-fieldset-row">
            <div>
              {field}:
            </div>
            <div>
              {value}
            </div>
          </div>
        );
      })}
    </div>
  )
}

module.exports = Fieldset