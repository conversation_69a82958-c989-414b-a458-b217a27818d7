const React = require('react');
const Details = require('./sections/Details');
const BondPremium = require('./sections/BondPremium');
const Equipment = require('./sections/Equipment');
const InvoiceSchedule = require('./sections/InvoiceSchedule');
const Materials = require('./sections/Materials');
const MiscExpenses = require('./sections/MiscExpenses');
const PayAppSchedule = require('./sections/PayAppSchedule');
const Payroll = require('./sections/Payroll');
const SubContractors = require('./sections/SubContractors');

const {
  TYPE_CONSTRUCTION,
  TYPE_PO_MANUFACTURING,
  TYPE_PO_INVENTORY,
} = require('../../helpers/globals');

const sections = [{
  key: 'details',
  component: Details,
  types: [TYPE_CONSTRUCTION, TYPE_PO_MANUFACTURING, TYPE_PO_INVENTORY]
},{
  key: 'payAppSchedule',
  component: PayAppSchedule,
  types: [TYPE_CONSTRUCTION]
},{
  key: 'invoiceSchedule',
  component: InvoiceSchedule,
  types: [TYPE_PO_MANUFACTURING]
},{
  key: 'payroll',
  component: Payroll,
  types: [TYPE_CONSTRUCTION, TYPE_PO_MANUFACTURING]
},{
  key: 'subContractors',
  component: SubContractors,
  types: [TYPE_CONSTRUCTION, TYPE_PO_MANUFACTURING]
},{
  key: 'materials',
  component: Materials,
  types: [TYPE_CONSTRUCTION, TYPE_PO_MANUFACTURING]
},{
  key: 'equipment',
  component: Equipment,
  types: [TYPE_CONSTRUCTION, TYPE_PO_MANUFACTURING]
},{
  key: 'bondPremium',
  component: BondPremium,
  types: [TYPE_CONSTRUCTION]
},{
  key: 'miscExpenses',
  component: MiscExpenses,
  types: [TYPE_CONSTRUCTION, TYPE_PO_MANUFACTURING]
}];

function Report({ params }) {

  const result = sections.map(({ 
    key, 
    types,
    component: Component
  }) => {

    const values = { ...params[key], meta: params.meta };

    if (typeof values === 'undefined' ||
      !types.includes(params.type.type)) {
      return '';
    }

    // Pass the type to the component
    values.type = params.type.type;

    return <Component key={key} {...values} />
  });

  return (
    <div>
      {result}
    </div>
  )
}

module.exports = Report;