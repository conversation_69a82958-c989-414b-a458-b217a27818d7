const React = require('react');
const Table = require('../Table');
const Section = require('../Section');

const {
  currency,
  formatDate,
} = require('../../../helpers/formatters');

function InvoiceSchedule(props) {
  return (
    <Section label="Invoice Schedule">
      <Table 
        headers={{
          date: {
            label: 'Date',
            formatter(value) {
              return formatDate(value)
            }
          },
          amount: {
            label: 'Amount',
            formatter(value) {
              return currency.format(value);
            }
          }
        }}
        rows={props.invoiceSchedule} />
    </Section>
  )
}

module.exports = InvoiceSchedule;