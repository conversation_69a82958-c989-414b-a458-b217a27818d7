const React = require('react');
const Fieldset = require('../Fieldset');
const Table = require('../Table');
const Section = require('../Section');

const {
  currency,
  formatDate,
  formatFrequency,
  capitalizeFirstLetter,
} = require('../../../helpers/formatters');

function Payroll({ hasPayroll, payrollFrequency, amountPerPeriod, amountVariation, payrollAllocations }) {

  if (hasPayroll === 'no') {
    return (
      <Section label="Payroll" noData />
    );
  }

  const fields = {
    'Frequency': formatFrequency(payrollFrequency),
    'Amount Variation': capitalizeFirstLetter(amountVariation),
  }

  if (amountVariation === 'fixed') {
    fields['Amount Per Period'] = currency.format(amountPerPeriod);
  }

  return (
    <Section label="Payroll">
      <Fieldset fields={fields} />
      {(amountVariation === 'variable') &&
        <Table 
          headers={{
            date: {
              label: 'Date',
              formatter(value) {
                return formatDate(value)
              }
            },
            amount: {
              label: 'Amount',
              formatter(value) {
                return currency.format(value);
              }
            }
          }}
          rows={payrollAllocations} />
      }
    </Section>
  );
}

module.exports = Payroll;