const React = require('react');
const Fieldset = require('../Fieldset');
const Section = require('../Section');
const {
  currency,
  formatDate,
} = require('../../../helpers/formatters');


function BondPremium({ hasBondPremium, totalAmount, dueDate }) {

  if (hasBondPremium === 'no') {
    return (
      <Section label="Bond Premium" noData />
    );
  }

  const fields = {
    'Total Amount': currency.format(totalAmount),
    'Due Date': formatDate(dueDate),
  }

  return (
    <Section label="Bond Premium">
      <Fieldset fields={fields} />
    </Section>
  )
}

module.exports = BondPremium;