const React = require('react');
const Fieldset = require('../Fieldset');
const Section = require('../Section');

const {
  currency,
  formatDate,
  formatFrequency,
} = require('../../../helpers/formatters');

const {
  TYPE_CONSTRUCTION,
  TYPE_PO_MANUFACTURING,
  TYPE_PO_INVENTORY,
} = require('../../../helpers/globals');

function Details(props) {

  const fields = {
    [TYPE_CONSTRUCTION] : {
      'User Name': props.userName,
      'User Email': props.userEmail,
      'User Company': props.userCompany,
      'User Company State': props.userCompanyState,
      'Start Date': formatDate(props.startDate),
      'Contract Value': currency.format(props.totalValue),
      'Total Estimated Direct Payroll': currency.format(props.totalEstimatedDirectPayroll),
      'Total Estimated Subcontract Labor': currency.format(props.totalEstimatedSubcontractLabor),
      'Total Estimated Material': currency.format(props.totalEstimatedMaterial),
      'Total Estimated Equipment Rental': currency.format(props.totalEstimatedEquipmentRental),
      'Total Estimated Bond Premium': currency.format(props.totalEstimatedBondPremium),
      'Total Estimated Misc Expenses': currency.format(props.totalEstimatedMiscExpenses),
      'Retainage': props.retainagePercent + '%',
      'Project Length': props.projectLengthWeeks + ' Weeks',
      'Pay App Frequency': formatFrequency(props.payAppFrequency),
      'Payment Delay': props.paymentDelayDays + ' Days',
      'Project Name': props.projectName,
      'Contractor Name': props.contractorName,
      'Margin': props.marginPercent + '%',
      'Money Received To-Date': currency.format(props.moneyReceivedToDate),
      'Joint Checks': props.jointChecks,
      'Referral Source': props.referralSource ?? '-'
    },
    [TYPE_PO_INVENTORY] : {
      'User Name': props.userName,
      'User Email': props.userEmail,
      'User Company': props.userCompany,
      'User Company State': props.userCompanyState,
      'Customer Name': props.customerName,
      'Total Value': currency.format(props.totalValue),
      'Payment Date': formatDate(props.paymentDate),
      'Total Cost': currency.format(props.totalCost),
      'Shipment Date': formatDate(props.shipmentDate),
      'Invoice Date': formatDate(props.invoiceDate),
      'Margin': props.marginPercent + '%',
      'Payment Delay': props.paymentDelayDays + ' Days',
    },
    [TYPE_PO_MANUFACTURING] : {
      'User Name': props.userName,
      'User Email': props.userEmail,
      'User Company': props.userCompany,
      'User Company State': props.userCompanyState,
      'Customer Name': props.customerName,
      'Total Value': currency.format(props.totalValue),
      'Total Estimated Direct Payroll': currency.format(props.totalEstimatedDirectPayroll),
      'Total Estimated Subcontract Labor': currency.format(props.totalEstimatedSubcontractLabor),
      'Total Estimated Material': currency.format(props.totalEstimatedMaterial),
      'Total Estimated Equipment Rental': currency.format(props.totalEstimatedEquipmentRental),
      'Total Estimated Misc Expenses': currency.format(props.totalEstimatedMiscExpenses),
      'Start Date': formatDate(props.startDate),
      'Project Length': props.projectLengthWeeks + ' Weeks',
      'Total Invoice': props.totalInvoices,
      'Margin': props.marginPercent + '%',
      'Payment Delay': props.paymentDelayDays + ' Days',
      'Referral Source': props.referralSource ?? '-'
    }
  }

  return (
    <Section label="Details">
      <Fieldset fields={fields[props.type]} />
    </Section>
  )
}

module.exports = Details;