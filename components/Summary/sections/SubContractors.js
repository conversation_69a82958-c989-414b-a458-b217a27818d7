const React = require('react');
const Fieldset = require('../Fieldset');
const Section = require('../Section');
const Table = require('../Table');
const { groupBy } = require('lodash');

const {
  currency,
  formatTerms,
  formatFrequency,
  formatDate,
  capitalizeFirstLetter
} = require('../../../helpers/formatters');

function SubContractors({ hasSubContractors, subContractors, meta }) {

  if (hasSubContractors === 'no') {
    return (
      <Section label="Sub Contractors" noData />
    );
  }

  const subs = groupBy(subContractors, 'name');

  return (
    <Section label="Sub Contractors">
      {Object.entries(subs).map(([sub, items]) =>
        <Section 
          key={sub}
          label={sub}
          heading="h3">
          {items.map((item, index) => {

            const fields = {
              'Scope of Work': item.scopeOfWork,
              'Amount Variation': capitalizeFirstLetter(item.amountVariation),
              'Payment Frequency': formatFrequency(item.paymentFrequency),
            }

            if (item.amountVariation === 'fixed') {
              switch(item.paymentFrequency) {
                case 'weekly':
                  fields['Weekly Amount'] = currency.format(item.amount);
                  break;
                case 'bi_weekly':
                  fields['Bi-Weekly Amount'] = currency.format(item.amount);
                  break;
                case 'monthly':
                  fields['Monthly Amount'] = currency.format(item.amount);
                  break;
                default:
                  break;
              }
              
              // Also display total amount
              fields['Total Amount'] = currency.format(item.amount * (meta.schedules[item.paymentFrequency]?.length ?? 1));
              fields['Payment Terms'] = formatTerms(item.paymentTerms);
            }

            const positivePayments = item.payments.filter(p => p.amount > 0);

            return (
              <div key={index}>
                <Fieldset fields={fields} />

                {(item.amountVariation === 'variable') &&
                  <div className='cfp-results-variables'>
                    {positivePayments.map((payment, index) => (
                      <div key={index} className='cfp-results-variables-row'>
                        <div>{formatDate(payment.date)}</div>
                        <div>{currency.format(payment.amount)}</div>
                      </div>
                    ))}
                  </div>
                }
              </div>
            );
          })}
        </Section>
      )}
    </Section>
  );
}

module.exports = SubContractors;