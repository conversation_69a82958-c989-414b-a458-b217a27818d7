const React = require('react');
const Table = require('../Table');
const Section = require('../Section');

const {
  currency,
  formatDate,
} = require('../../../helpers/formatters');

function PayAppSchedule(props) {
  return (
    <Section label="Pay App Schedule">
      <Table 
        headers={{
          date: {
            label: 'Date',
            formatter(value) {
              return formatDate(value);
            }
          },
          amount: {
            label: 'Amount',
            formatter(value) {
              return currency.format(value);
            }
          }
        }}
        rows={props.payAppAllocations} />
    </Section>
  )
}

function InvoiceSchedule(props) {
  return (
    <Section label="Invoice Schedule">
      <Table 
        headers={{
          date: {
            label: 'Date',
            formatter(value) {
              return formatDate(value)
            }
          },
          amount: {
            label: 'Amount',
            formatter(value) {
              return currency.format(value);
            }
          }
        }}
        rows={props.invoiceSchedule} />
    </Section>
  )
}

module.exports = PayAppSchedule;