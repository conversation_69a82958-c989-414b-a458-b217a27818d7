const React = require('react');
const filterObject = require('../../helpers/filterObject');

function Table({ headers, rows }) {

  const headerKeys = Object.keys(headers);

  return (
    <table className="cfp-results-table">
      <thead>
        <tr>
          {Object.values(headers).map(header =>
            <th key={header.label}>
              {header.label}
            </th>
          )}
        </tr>
      </thead>
      <tbody>
        {rows.map((row, index) => {

          const entries = Object.entries(filterObject(row, headerKeys));
          
          return (
            <tr key={index}>
              {entries.map(([field, value]) => {

                const config = headers[field];

                if (typeof config.formatter === 'function') {
                  value = config.formatter(value);
                }

                return (
                  <td key={`${field}-${value}`}>
                    {value}
                  </td>
                );
              })}
            </tr>
          );
        })}
      </tbody>
    </table>
  )
}

module.exports = Table