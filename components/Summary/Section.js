const React = require('react');

function Section({ label, noData, children, heading: Heading = 'h2' }) {

  return (
    <div className="cfp-results-section">
      <Heading className="cfp-results-section-heading">
        {label}
      </Heading>
      {noData &&
        <div className="cfp-results-section-no-data">
          {`No ${label} selected`}
        </div>
      }
      {<PERSON>olean(children) &&
        <div className="cfp-results-section-data">
          {children}
        </div>
      }
    </div>
  )
}

module.exports = Section;