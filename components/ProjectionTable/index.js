const React = require('react');
const classnames = require('classnames');
const moment = require('moment');
const { formatNumber } = require('../../helpers/formatters');

function ProjectionTable(props) {

  const { rowHeadings, schedule } = props;

  const rowHeadingsClone = { ...rowHeadings };

  // Discard the cash position headings since they're for 
  // the XLS only (for now)
  delete rowHeadingsClone.totalExpenses;
  delete rowHeadingsClone.cashPosition;
  delete rowHeadingsClone.payment;
  delete rowHeadingsClone.cashFlow;
  delete rowHeadingsClone.cumulativeCashFlow;

  const scheduleWeeks = Object.keys(schedule);

  return (
    <div>
      <table className="cfs-table">
        <thead>
          <tr>
            <td></td>
            {scheduleWeeks.map(scheduleWeek =>
              <th key={scheduleWeek}>
                {moment(scheduleWeek).format('M/D/YYYY')}
              </th>
            )}
          </tr>
        </thead>
        <tbody>
          {Object.keys(rowHeadingsClone).map(sectionKey => {

            const section = rowHeadingsClone[sectionKey];
            const hasChildren = Boolean(section.children?.length);

            return (
              <React.Fragment key={sectionKey}>
                <tr className={classnames({
                  'cfs-group-separator': section.separator,
                  'cfs-group-row': hasChildren
                })}>
                  <th>{section.label}</th>
                  {scheduleWeeks.map(scheduleWeek => {
                    const amount = schedule?.[scheduleWeek]?.[sectionKey];
                    return (
                      <td key={sectionKey}>
                        {formatNumber(amount, section.format)}
                      </td>
                    );
                  })}
                </tr>
                {hasChildren &&
                  <>
                    {section.children.map(child =>
                      <tr 
                        key={child}
                        className="cfs-child">
                        <th>{child}</th>
                        {scheduleWeeks.map(scheduleWeek => {
                          const amount = schedule?.[scheduleWeek]?.[sectionKey]?.[child];
                          return (
                            <td key={scheduleWeek}>
                              {formatNumber(amount, section.format)}
                            </td>
                          );
                        })}
                      </tr>
                    )}
                  </>
                }
              </React.Fragment>
            );
          })}
        </tbody>
      </table>
    </div>
  )
}

module.exports = ProjectionTable;