const React = require('react');
const {
  FORMAT_CURRENCY,
  FORMAT_PERCENT,
} = require('../../helpers/globals');
const { formatNumber } = require('../../helpers/formatters');
const { default: classNames } = require('classnames');

const getArrayTotal = (arr) => arr.reduce((acc, curr) => (curr || 0) + acc, 0);

const getPayrollTotalCosts = (data, meta) => {
  if (data.amountVariation === 'variable') {
    return getArrayTotal(
      data.payrollAllocations.map((item) => item.amount)
    )
  } else if (data.payrollFrequency) {
    const schedule = meta.schedules[data.payrollFrequency];
    return data.amountPerPeriod * schedule.length;
  }

  return 0;
}

const calculateTotals = ({ schedule, params }) => {
  const totals = {};

  // Calculate payroll expenses
  totals['payroll'] = getPayrollTotalCosts(params.payroll, params.meta);

  // Calculate for array based expenses
  ['subContractors', 'materialOrders', 'equipmentVendors', 'miscExpenses'].forEach(key => {
    totals[key] = Object.values(schedule).reduce((acc, curr) => (
      getArrayTotal(Object.values(curr[key] ?? {})) + acc
    ), 0);
  });

  totals['bondPremium'] = params.bondPremium.totalAmount;

  return totals;
};

function TotalsReview(props) {
  // Better to check on the props if it is a Construction or a PO
  const totalIncome = Object.values(props.schedule).reduce((acc, curr) => (curr.payApp || curr.invoice || 0) + acc, 0);
  const totals = calculateTotals(props);
  const totalExpenses = getArrayTotal(Object.values(totals));

  const margin = totalIncome - totalExpenses;

  const renderExpenseRow = (step, label, estimated, value) => {
    const difference = (value - estimated).toFixed(2);
    const absoluteDifferent = Math.abs(difference);

    return (
      <tr className='cft-bg-light'>
        <td>{label}</td>
        <td>
          <span>{formatNumber(value, FORMAT_CURRENCY)}</span>
          <button data-step={step} className='button button--text button--small'>edit</button>
        </td>
        <td
          className={classNames({
            'cft-color-red': absoluteDifferent !== 0
          })}
        >
          <span>
            {formatNumber(estimated, FORMAT_CURRENCY)}
            {absoluteDifferent !== 0 ? ` (${formatNumber(difference / estimated, FORMAT_PERCENT)})` : ''}
          </span>
          <button data-step='1' className='button button--text button--small'>edit</button>
        </td>
      </tr>
    )
  }

  return (
    <div className='cft-review-table'>
      <table>
        <thead>
          <tr>
            <th></th>
            <th>Actual</th>
            <th>Your Estimate</th>
          </tr>
        </thead>

        <tbody>
          <tr>
            <td>Total Income</td>
            <td>{formatNumber(totalIncome, FORMAT_CURRENCY)}</td>
            <td></td>
          </tr>
          <tr className='cft-bg-light'>
            <td>Payroll</td>
            <td>
              <span>{formatNumber(totals.payroll, FORMAT_CURRENCY)}</span>
              <button data-step='3' className='button button--text button--small'>edit</button>
            </td>
            
            <td
              className={classNames({
                'cft-color-red': totals.payroll !== props.params.details.totalEstimatedDirectPayroll
              })}
            >
              <span>
                {formatNumber(props.params.details.totalEstimatedDirectPayroll, FORMAT_CURRENCY)}
                <button data-step='1' className='button button--text button--small'>edit</button>
              </span>
            </td>
          </tr>

          {renderExpenseRow(4, 'Sub Contractors', props.params.details.totalEstimatedSubcontractLabor, totals.subContractors)}
          {renderExpenseRow(5, 'Material Orders', props.params.details.totalEstimatedMaterial, totals.materialOrders)}
          {renderExpenseRow(6, 'Equipment Vendors', props.params.details.totalEstimatedEquipmentRental, totals.equipmentVendors)}
          {renderExpenseRow(7, 'Bond Premium', props.params.details.totalEstimatedBondPremium, totals.bondPremium)}
          {renderExpenseRow(8, 'Misc Expenses', props.params.details.totalEstimatedMiscExpenses, totals.miscExpenses)}
          <tr>
            <td>Total Margin</td>
            <td
              className={classNames({
                'cft-color-red': margin < 0
              })}
            >
              {formatNumber(margin, FORMAT_CURRENCY)} ({formatNumber(margin / totalIncome, FORMAT_PERCENT)})
            </td>
            <td></td>
          </tr>
        </tbody>
        
      </table>
    </div>
  )
}

module.exports = TotalsReview;