const CashFlow = require('../CashFlow');
const render = require('../render');
const ProjectionTable = require('../components/ProjectionTable');
const TotalsReview = require('../components/TotalsReview');
const Summary = require('../components/Summary');

/**
 * Consolidated function to process cash flow
 * input and render relevant html components for
 * display purposes.
 * 
 * @param {Object} params 
 * @returns {Object}
*/
function getProcessorResults(params) {
  const cashFlow = new CashFlow(params)
  const result = cashFlow.process();

  return {
    ...result,
    html: {
      summary: render(Summary, result),
      totalsReview: render(TotalsReview, result),
      table: render(ProjectionTable, result)
    }
  }
}

module.exports = getProcessorResults;