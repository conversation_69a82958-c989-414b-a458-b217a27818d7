function handleResponse(statusCode, data) {

  const response = {
    statusCode,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Allow-Methods': 'OPTIONS,GET,POST'
    }
  }

  if (typeof data === 'object') {
    response.headers['Content-Type'] = 'application/json';
    response.body = JSON.stringify(data);
  } else {
    response.headers['Content-Type'] = 'text/html; charset=UTF-8';
    response.body = data;
  }

  return response;
}

module.exports = handleResponse;