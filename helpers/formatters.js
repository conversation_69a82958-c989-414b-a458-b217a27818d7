const moment = require('moment');

const {
  PAYMENT_TERM_COD,
  PAYMENT_TERM_NET_30,
  PAYMENT_TERM_NET_45,
  PAYMENT_TERM_NET_60,
  FORMAT_CURRENCY,
  FORMAT_PERCENT,
} = require('./globals');

const currency = new Intl.NumberFormat('en-US', { 
  style: 'currency', 
  currency: 'USD' 
});

const percent = new Intl.NumberFormat('en-US', {
  style: 'percent',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2
});

const formatNumber = (value, format) => {
  const map = {
    [FORMAT_CURRENCY]: currency,
    [FORMAT_PERCENT]: percent
  }

  const formatter = map[format] || currency;

  return (
    isFinite(value) 
      ? formatter.format(value)
      : ''
  );
}

function formatDate(value) {
  return moment(value).format('M/D/YYYY');
}

function formatTerms(value) {
  const map = {
    [PAYMENT_TERM_COD]: 'COD',
    [PAYMENT_TERM_NET_30]: 'Net 30',
    [PAYMENT_TERM_NET_45]: 'Net 45',
    [PAYMENT_TERM_NET_60]: 'Net 60',
  }

  return map[value];
}

function formatFrequency(value) {
  const map = {
    weekly: 'Weekly',
    bi_weekly: 'Bi-Weekly',
    monthly: 'Monthly',
    paid_when_paid: 'Paid When Paid',
    one_time: 'One Time',
  }

  return map[value];
}

function formatExpenseTypes(value) {
  const map = {
    gas: 'Gas',
    hotels: 'Hotels',
    travel: 'Travel',
    rent: 'Rent',
    utilities: 'Utilities',
    other: 'Other'
  };

  return map[value];
} 

function capitalizeFirstLetter(string) {
  if (!string) {
    return '';
  }

  return string.charAt(0).toUpperCase() + string.slice(1);
}

module.exports = {
  currency,
  formatNumber,
  formatDate,
  formatTerms,
  formatFrequency,
  formatExpenseTypes,
  capitalizeFirstLetter,
}