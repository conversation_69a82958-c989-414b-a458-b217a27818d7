
exports.TYPE_CONSTRUCTION = 'construction';
exports.TYPE_PO_MANUFACTURING = 'po_manufacturing';
exports.TYPE_PO_INVENTORY = 'po_inventory';

exports.STEP_TYPE_KEY = 'type';
exports.STEP_DETAILS_KEY = 'details';
exports.STEP_PAY_APP_SCHEDULE_KEY = 'payAppSchedule';
exports.STEP_INVOICE_SCHEDULE_KEY = 'invoiceSchedule';
exports.STEP_PAYROLL_KEY = 'payroll';
exports.STEP_SUB_CONTRACTORS_KEY = 'subContractors';
exports.STEP_MATERIALS_KEY = 'materials';
exports.STEP_EQUIPMENT_KEY = 'equipment';
exports.STEP_BOND_PREMIUM_KEY = 'bondPremium';
exports.STEP_MISC_EXPENSES_KEY = 'miscExpenses';
exports.STEP_REVIEW_KEY = 'review';

exports.FIELD_SUB_CONTRACTORS = 'subContractors';
exports.FIELD_MATERIAL_ORDERS = 'materialOrders';
exports.FIELD_EQUIPMENT_VENDORS = 'equipmentVendors';
exports.FIELD_MISC_EXPENSES = 'miscExpenses';

exports.COLOR_WHITE = 'ffffff';
exports.COLOR_BLUE = '1a3086';
exports.COLOR_GREEN = '2cac68';

exports.FORMAT_CURRENCY = 'currency';
exports.FORMAT_PERCENT = 'percent';

exports.BORDER_THIN = 'thin';

exports.TOTAL_SUM = 'sum';

exports.PAYMENT_TERM_COD = 'cod';
exports.PAYMENT_TERM_NET_30 = 'net_30';
exports.PAYMENT_TERM_NET_45 = 'net_45';
exports.PAYMENT_TERM_NET_60 = 'net_60';

exports.PAYMENT_TERM_DAYS_MAP = {
  [exports.PAYMENT_TERM_COD]: 0,
  [exports.PAYMENT_TERM_NET_30]: 30,
  [exports.PAYMENT_TERM_NET_45]: 45,
  [exports.PAYMENT_TERM_NET_60]: 60,
}

exports.PAYMENT_FREQUENCY_WEEKLY = 'weekly';
exports.PAYMENT_FREQUENCY_BI_WEEKLY = 'bi_weekly';
exports.PAYMENT_FREQUENCY_MONTHLY = 'monthly';
exports.PAYMENT_FREQUENCY_PAID_WHEN_PAID = 'paid_when_paid';
exports.PAYMENT_FREQUENCY_ONE_TIME = 'one_time';
