
const handleResponse = require('./helpers/handleResponse');
const getProcessorResults = require('./helpers/getProcessorResults');

/**
 * Handles HTTP requests to preview the results
 * 
 * @param {Object} event 
 * @returns {Object}
 */
exports.handlePreviewRequest = async (event) => {
  params = JSON.parse(event.body);

  const data = getProcessorResults(params);

  return handleResponse(200, data);
}

/**
 * Handles direct input and output to be used by
 * other Lambda functions, specifically, the PDF and
 * XLS generators
 * 
 * @param {*} event 
 * @returns 
 */
exports.handleProcessCashFlow = async (event) => {
  return getProcessorResults(event);
}
