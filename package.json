{"name": "mf-cash-flow-service", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"process": "node tests/processLocal.js params-construction.json", "process-po-mfg": "node tests/processLocal.js params-po-manufacturing.json", "process-po-inventory": "node tests/processLocal.js params-po-inventory.json", "process-components": "node tests/processLocalComponents.js params-construction.json"}, "author": "", "license": "ISC", "dependencies": {"@babel/core": "^7.15.0", "@babel/preset-react": "^7.14.5", "@babel/register": "^7.15.3", "classnames": "^2.3.1", "lodash": "^4.17.21", "moment": "^2.29.1", "moment-range": "^4.0.2", "react": "^17.0.2", "react-dom": "^17.0.2"}, "devDependencies": {"jest": "^26.6.3"}}