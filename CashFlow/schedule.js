const Moment = require('moment');
const { extendMoment } = require('moment-range');
const { PAYMENT_TERM_DAYS_MAP } = require('../helpers/globals');

const moment = extendMoment(Moment);

/**
 * Retrieves the next Friday from the start date
 * 
 * @param {*} startDateMoment 
 * @returns
 */
function getNextFriday(date) {
  const dateMoment = moment(date);

  // If the start date is on or before Friday
  if (dateMoment.day() <= 5) { 
    // Return the Friday in the current week
    return dateMoment.day(5);
  } 
  // Otherwise return the next Friday
  else {
    return dateMoment.add(1, 'weeks').day(5);
  }
};

/**
 * Retrieves the last Friday of the month for a given date
 * 
 * @param {*} monthMoment 
 * @returns 
 */
function getLastFridayOfMonth(date) {
  const dateMoment = moment(date);

  const lastDay = dateMoment.endOf('month').startOf('day');
  let sub;
  if (lastDay.day() >= 5) {
    sub = lastDay.day() - 5;
  } else {
    sub = lastDay.day() + 2;
  }
  return lastDay.subtract(sub, 'days');
};

/**
 * Retrieves the transaction date based on a target date
 * and the number of days to delay it
 * 
 * @param {string|Moment} date 
 * @param {number} delayDays 
 * @returns {string}
 */
function getDelayedTransactionDate(date, delayDays) {
  const transactionDate = moment(date).add(delayDays, 'days');
  return getNextFriday(transactionDate).format('YYYY-MM-DD');
}

/**
 * 
 * @param {string|Moment} date The transaction date
 * @param {string} term The payment term
 * @returns 
 */
function getTermPaymentDate(date, term) {
  const delay = PAYMENT_TERM_DAYS_MAP[term];
  return getDelayedTransactionDate(date, delay);
}


/**
 * Retrieves the last Friday of the month for a given date
 * 
 * @param {*} monthMoment 
 * @returns 
 */
 function getLastFridayOfMidMonth(date) {
  const dateMoment = moment(date);

  const lastDay = dateMoment.startOf('month').startOf('day').add(14, 'days');
  let sub;
  if (lastDay.day() >= 5) {
    sub = lastDay.day() - 5;
  } else {
    sub = lastDay.day() + 2;
  }
  return lastDay.subtract(sub, 'days');
};


/**
 * Retrieves an array of each friday within the schedule
 * 
 * @param {string} startDate 
 * @param {number} projectLengthWeeks 
 * @returns 
 */
function getWeeklyIntervals(startDate, projectLengthWeeks) {
  const firstFridayMoment = getNextFriday(startDate);

  const range = moment.rangeFromInterval('week', projectLengthWeeks - 1, firstFridayMoment);

  return Array.from(range.by('week')).map(date => {
    return date.format('YYYY-MM-DD');
  });
};

/**
 * Retrieves an array of each Friday before before or on the 15th 
 * and last day of each month
 * 
 * @param {string} startDate 
 * @param {number} projectLengthWeeks 
 * @returns 
 */
 function getBiWeeklyIntervals(startDate, projectLengthWeeks) {
  const stateDateMoment = moment(startDate);
  const endDateMoment = moment(startDate).add(projectLengthWeeks - 1, 'weeks');

  // The range function doesn't work 100% unless you give it whole months
  const range = moment.range(
    moment(stateDateMoment).startOf('month'), 
    moment(endDateMoment).endOf('month')
  );

  const intervals = [];

  const months = Array.from(range.by('month'));

  months.forEach((month, index) => {

    // Get the last Friday on or before the 15th
    const midFriday = getLastFridayOfMidMonth(month);

    // Ensure the date falls within the schedule
    if (midFriday.isSameOrAfter(stateDateMoment) 
      && midFriday.isSameOrBefore(endDateMoment)){
      intervals.push(midFriday.format('YYYY-MM-DD'));
    }

    // For the last month if the end date falls within the
    // first payment date of the project add it
    if ((index === (months.length -1))
      && endDateMoment.isSameOrBefore(midFriday)) {
      intervals.push(midFriday);
    }

    // Get the last Friday of the month
    const lastFriday = getLastFridayOfMonth(month);

    // Ensure the date falls within the schedule
    if (lastFriday.isSameOrAfter(stateDateMoment) 
      && lastFriday.isSameOrBefore(endDateMoment)){
      intervals.push(lastFriday.format('YYYY-MM-DD'));
    }

    // For the last month if the end date falls within the
    // second payment date of the project add it
    if ((index === (months.length -1))
      && endDateMoment.isAfter(midFriday)
      && endDateMoment.isSameOrBefore(lastFriday)) {
      intervals.push(lastFriday);
    }
    
  });

  return intervals;
};

/**
 * Gets the default app schedule by month on the last Friday of the month
 * based on the number of weeks in the project
 * 
 * @param {string} startDate 
 * @param {number} projectLengthWeeks 
 * @returns 
 */
function getMonthlyIntervals(startDate, projectLengthWeeks) {
  const startDateMoment = moment(startDate);
  const endDateMoment = moment(startDate).add(projectLengthWeeks - 1, 'weeks');

  let firstMonthPaymentDate = getLastFridayOfMonth(startDate);

  // If the first Friday is not after the start date then we need to move it to the next month
  if (!firstMonthPaymentDate.isSameOrAfter(startDateMoment)) {
    const nextMonthMoment = moment(startDate).add(1, 'month');
    firstMonthPaymentDate = getLastFridayOfMonth(nextMonthMoment);
  }

  const lastMonthPaymentDate = getLastFridayOfMonth(endDateMoment);

  const range = moment.range(
    firstMonthPaymentDate.startOf('month'),
    lastMonthPaymentDate.endOf('month')
  );

  return Array.from(range.by('month')).map(date => {
    return getLastFridayOfMonth(date).format('YYYY-MM-DD');
  });
};

/**
 * Retrieves placeholders for every week in the project
 * 
 * @param {String} startDate 
 * @param {Number} projectLengthWeeks 
 * @returns {Array}
 */
function getProjectWeeks({
  startDate,
  projectLengthWeeks,
  payAppFrequency,
  paymentDelayDays
}) {

  const endDateMoment = moment(startDate)
                          // Don't subtract the last week to ensure there's
                          // padding for PO Manufacturing
                          .add(projectLengthWeeks, 'weeks')

  const firstFridayMoment = getNextFriday(startDate);

  let lastFridayMoment = getNextFriday(endDateMoment);

  // If this project is paid monthly we need to extend the weeks out
  // to account for the payment delay in the last month
  if (payAppFrequency === 'monthly') {
    lastFridayMoment = getLastFridayOfMonth(endDateMoment)
  }

  // Determine how many days to pad the schedule based on
  // the payment delay (for "Paid When Paid" frequencies) 
  // or 60 days since we allow expenses to be entered 60 days
  // after the end date. So this covers both scenarios.
  const trailingDays = Math.max(paymentDelayDays, 60);

  const range = moment.range(
    firstFridayMoment, 
    getDelayedTransactionDate(lastFridayMoment, trailingDays)
  );

  return Array.from(range.by('week')).map(date => {
    return date.format('YYYY-MM-DD')
  });
}

/**
 * Generates intervals based off the invoice/pay app schedule by adding
 * the appropriate delays to each one.
 * 
 * @param {Array} invoiceIntervals
 * @param {Number} paymentDelayDays 
 * @returns {Array}
 */
function getPaidWhenPaidIntervals(invoiceIntervals, paymentDelayDays) {
  return invoiceIntervals.map(({ date }) => {
    return getDelayedTransactionDate(date, paymentDelayDays);
  })
}

/**
 * Builds a weekly schedule of Fridays between
 * an array of dates
 * 
 * @param {Array} dates 
 * @returns {Array}
 */
function getWeeklyIntervalsBetweenDates(dates) {

    const moments = dates.map(d => moment(d));

    const startDateMoment = moment.min(moments);
    const endDateMoment = moment.max(moments);

    const firstFridayMoment = getNextFriday(startDateMoment);
    const lastFridayMoment = getNextFriday(endDateMoment);

    const range = moment.range(firstFridayMoment, lastFridayMoment);

    return Array.from(range.by('week')).map(date => {
      return date.format('YYYY-MM-DD')
    });
} 

module.exports = {
  getNextFriday,
  getLastFridayOfMonth,
  getWeeklyIntervals,
  getWeeklyIntervalsBetweenDates,
  getBiWeeklyIntervals,
  getMonthlyIntervals,
  getPaidWhenPaidIntervals,
  getProjectWeeks,
  getDelayedTransactionDate,
  getTermPaymentDate,
}