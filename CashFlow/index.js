const { sortedIndexBy } = require('lodash');
const {
  getProjectWeeks,
  getWeeklyIntervals,
  getBiWeeklyIntervals,
  getMonthlyIntervals,
  getPaidWhenPaidIntervals,
  getNextFriday,
  getWeeklyIntervalsBetweenDates,
  getDelayedTransactionDate,
  getTermPaymentDate
} = require('./schedule');

const {
  TYPE_CONSTRUCTION,
  TYPE_PO_MANUFACTURING,
  TYPE_PO_INVENTORY,

  COLOR_WHITE,
  COLOR_BLUE,
  COLOR_GREEN,

  FORMAT_CURRENCY,
  FORMAT_PERCENT,
  
  BORDER_THIN,

  TOTAL_SUM,
  PAYMENT_TERM_COD,
  PAYMENT_FREQUENCY_ONE_TIME,
  FIELD_SUB_CONTRACTORS,
  FIELD_MATERIAL_ORDERS,
  FIELD_EQUIPMENT_VENDORS,
  FIELD_MISC_EXPENSES,
} = require('../helpers/globals');


/**
 * This class is designed as the primary processing engine
 * to convert params generated from the react form on the website
 * into a cash flow schedule for underwriting along with associated 
 * computations
 * 
 * @property {Object} schedule The primary schedule object to track transaction milestones
 * @property {Object} intervals The schedule for each of the four interval types
 * @property {Object} params The params received from the react form
 */
class CashFlow {

  schedule = {};

  intervals = {}

  params = null;

  rowHeadings = {};

  /**
   * Primary constructor
   * 
   * @param {Object} params 
   */
  constructor(params) {
    this.params = params;
  }

  /**
   * Primary interface to process the cash flow model
   * 
   * @returns {Object}
   */
  process() {

    const { type } = this.params.type

    // PO - Invoice flow use their own processor
    if (type === TYPE_PO_INVENTORY) {

      this.buildPoInvoiceFlow();

      this.buildRowHeadings();
      
      return {
        id: this.params.id, // Move the ID up for emphasis
        intervals: this.intervals,
        schedule: this.schedule,
        params: this.params,
        rowHeadings: this.rowHeadings
      }
    }

    // From here follow the standard income/expense flow
    
    // Step 1: Build out the schedule so we have something to populate
    this.buildSchedule();

    // Step 2: Build out the expense intervals so we can easily flow expenses
    this.buildExpenseIntervals();

    // Step 3 (a): Convert the allocations into actual payments
    if (type === TYPE_CONSTRUCTION) {
      this.processPayApps();
    
    // Step 3 (b): Flow the invoices into the correct dates
    } else if (type === TYPE_PO_MANUFACTURING) {
      this.processInvoices();
    }

    // Step 4: Flow payroll into the schedule
    this.processPayrollExpenses();

    // Step 5: Flow individual contractor expenses into the schedule
    this.processSubContractorExpenses();

    // Step 6: Flow individual material expenses into the schedule
    this.processMaterialExpenses();

    // Step 7: Flow individual equipment vendor expenses into the schedule
    this.processEquipmentExpenses();

    // Step 8: Flow the bond premium expense into the appropriate Friday
    if (type === TYPE_CONSTRUCTION) {
      this.processBondPremiumExpenses();
    }

    // Step 9: Flow individual misc expenses into the schedule
    this.processMiscExpenses();

    // Step 10: Flow retainage to the end of the schedule
    if (type === TYPE_CONSTRUCTION) {
      this.processRetainagePayment();
    }

    // Step 11: Build up row headings for tables & spreadsheets
    this.buildRowHeadings();

    return {
      id: this.params.id, // Move the ID up for emphasis
      intervals: this.intervals,
      schedule: this.schedule,
      params: this.params,
      rowHeadings: this.rowHeadings
    }
  }

  /**
   * Use the details below to build out the entire PO - Invoice
   * model.
   * 
   * {
   *   "details": {
   *     "customerName": "John Doe",
   *     "totalValue": 100000,
   *     "paymentDate": "2021-10-01",
   *     "totalCost": 50000,
   *     "shipmentDate": "2021-10-15",
   *     "invoiceDate": "2021-10-15",
   *     "marginPercent": 5,
   *     "paymentDelayDays": 15
   *   }
   * }
   */
  buildPoInvoiceFlow() {

    const {
      totalValue,
      totalCost,
      paymentDate,
      invoiceDate,
      shipmentDate
    } = this.params.details;

    const weeks = getWeeklyIntervalsBetweenDates([
      paymentDate,
      invoiceDate,
      shipmentDate
    ]);

    this.schedule = weeks.reduce((obj, week) => {
      obj[week] = {};
      return obj;
    }, {});

    const invoiceDateFridayMoment = getNextFriday(invoiceDate);
    const paymentDateFridayMoment = getNextFriday(paymentDate);

    this.schedule[invoiceDateFridayMoment.format('YYYY-MM-DD')].invoice = totalValue;
    this.schedule[paymentDateFridayMoment.format('YYYY-MM-DD')].payment = totalCost;
  }

  /**
   * Use the weeks to create a baseline schedule object with each
   * week as a key.  This will make it easier to assign each 
   * transaction to the appropriate week.
   */
  buildSchedule() {

    // Get an array of the project's weeks (or each Friday)
    const weeks = getProjectWeeks(this.params.details);

    // Add to weeks milestone dates, and ensure it is sorted
    if (this.params.details.payAppFrequency === 'milestone') {
      this.params.payAppSchedule.payAppAllocations.forEach((payApp) => {
        const index = sortedIndexBy(weeks, payApp.date, dateString => new Date(dateString));

        weeks.splice(index, 0, payApp.date);
      })
    }

    // Convert the weeks into an object where the date string of every Friday
    // becomes the key in the first level of the object
    this.schedule = weeks.reduce((obj, week) => {
      // Prep the default objects
      obj[week] = {
        [FIELD_SUB_CONTRACTORS]: {},
        [FIELD_MATERIAL_ORDERS]: {},
        [FIELD_EQUIPMENT_VENDORS]: {},
        [FIELD_MISC_EXPENSES]: {}
      };
      return obj;
    }, {});
  }

  /**
   * Build intervals of date strings matching up with the master schedule
   * to match each available type of input
   */
  buildExpenseIntervals() {
    const {
      type: {
        type
      },
      details: {
        startDate,
        projectLengthWeeks,
        paymentDelayDays,
      },
      payAppSchedule: {
        payAppAllocations
      },
      invoiceSchedule: {
        invoiceSchedule
      }
    } = this.params;

    let invoiceIntervals = [];
    if (type === TYPE_CONSTRUCTION) {
      invoiceIntervals = payAppAllocations;
    } else if (type === TYPE_PO_MANUFACTURING) {
      invoiceIntervals = invoiceSchedule;
    }

    // Align each type of interval (in line with received params) with
    // the appropriate interval schedule using the appropriate helpers
    this.intervals = {
      weekly: getWeeklyIntervals(startDate, projectLengthWeeks),
      bi_weekly: getBiWeeklyIntervals(startDate, projectLengthWeeks),
      monthly: getMonthlyIntervals(startDate, projectLengthWeeks),
      paid_when_paid: getPaidWhenPaidIntervals(invoiceIntervals, paymentDelayDays),
    }
  }

  /**
   * Handles converting pay app allocations (e.g. the % of total owed on 
   * specific milestones) as shown below into the appropriate week.
   * 
   * ```json
   * "payAppSchedule": {
   *   "allocationType": "variable",
   *   "payAppAllocations": [{
   *     "date": "2021-10-29",
   *     "allocation": 14.2
   *   }]
   * }
   * ``` 
   */
  processPayApps() {

    const {
      details: {
        retainagePercent,
        paymentDelayDays
      },
      payAppSchedule: {
        payAppAllocations
      }
    } = this.params;

    // Don't include retainage in the money allocated
    const retainage = retainagePercent / 100;
    
    payAppAllocations.forEach(payApp => {
      const { date, amount } = payApp;

      // Skip because an initial milestone pay app allocation is empty
      if (!date) {
        return;
      }

      const totalIncome = amount * (1 - retainage);

      this.schedule[date].payApp = amount;
      this.schedule[date].retainagePercent = retainagePercent / 100
      this.schedule[date].retainage = -(amount * retainage);
      this.schedule[date].totalIncome = totalIncome;


      // Append the cash received based on the payment delay
      const paymentDate = getDelayedTransactionDate(date, paymentDelayDays);

      this.schedule[paymentDate].payment = totalIncome; // @todo confirm if net pay app is correct
    });

    
  }

  /**
   * Converts invoices in the following format into
   * expense line items within the master schedule.
   * 
   * ```json
   * "invoiceSchedule": {
   *   "invoiceSchedule": [{
   *     "date": "2021-10-29",
   *     "amount": 10000
   *   }]
   * }
   * ```
   */
  processInvoices() {
    const {
      details: {
        paymentDelayDays
      },
      invoiceSchedule: {
        invoiceSchedule
      }
    } = this.params;

    invoiceSchedule.forEach(invoice => {

      if (!invoice.date) {
        return;
      }

      const invoiceDateFridayMoment = getNextFriday(invoice.date);
      const date = invoiceDateFridayMoment.format('YYYY-MM-DD'); 
      const existingAmount = this.schedule[date]?.invoice || 0;
      const amount = existingAmount + invoice.amount

      // Set the primary invoice amount
      this.schedule[date].invoice = amount;

      // Append the cash received based on the payment delay
      const paymentDate = getDelayedTransactionDate(date, paymentDelayDays);
      this.schedule[paymentDate].payment = amount; // @todo confirm if net pay app is correct
    });  
  }
  
  /**
   * Converts payroll expense params in the following format into
   * expense line items within the master schedule.
   * 
   * ```json
   * "payroll": {
   *   "hasPayroll": "yes",
   *   "payrollFrequency": "bi_weekly",
   *   "amountPerPeriod": 100000
   * }
   * ```
   */
  processPayrollExpenses() {
    const {
      hasPayroll,
      payrollFrequency,
      amountPerPeriod,
      amountVariation,
      payrollAllocations,
    } = this.params.payroll;

    if (hasPayroll === 'no') {
      return;
    }

    if (payrollFrequency && amountVariation === 'fixed') {
      const payrollIntervals = this.intervals[payrollFrequency];

      payrollIntervals.forEach(date => {
        this.schedule[date].payrollExpense = amountPerPeriod
      });
    } else if (amountVariation === 'variable') {
      payrollAllocations.forEach(({ date, amount }) => {
        this.schedule[date].payrollExpense = amount
      })
    }
  }

  /**
   * Converts subcontractor  params in the following format into
   * expense line items within the master schedule on a variable
   * basis based on the next/current Friday from the payment date.  
   * Note that each expense will be tied to the subcontractor name
   * 
   * ```json
   * "subContractors": {
   *   "hasSubContractors": "yes",
   *   "subContractors": [{
   *     "name": "Danny",
   *     "scopeOfWork": "Something",
   *     "amount": 10000,
   *     "paymentDate": "2021-11-17"
   *   }]
   * }
   * ```
   */
  processSubContractorExpenses() {
    const {
      hasSubContractors,
      subContractors
    } = this.params.subContractors;

    if (!subContractors?.length || hasSubContractors === 'no') {
      return;
    }

    subContractors.forEach((record) => {
      this.buildExpenseSchedule(FIELD_SUB_CONTRACTORS, record);
    });
  }

  /**
   * Converts material params in the following format into
   * expense line items within the master schedule on a variable
   * basis based on the next/current Friday from the payment date.  
   * Note that each expense will be tied to the supplier's name.
   * 
   * ```json
   * "materials": {
   *   "hasMaterials": "yes",
   *   "materialOrders": [{
   *     "name": "Costco",
   *     "currentBalance": 10000,
   *     "amount": 60000,
   *     "orderDate": "2021-09-25",
   *     "paymentDate": "2021-09-28"
   *   }
   * }
   * ```
   */
  processMaterialExpenses() {
    const {
      hasMaterials,
      materialOrders
    } = this.params.materials;

    if (!materialOrders?.length || hasMaterials === 'no') {
      return;
    }

    materialOrders.forEach((record) => {
      this.buildExpenseSchedule(FIELD_MATERIAL_ORDERS, record);
    });
  }

  /**
   * Converts equipment  params in the following format into
   * expense line items within the master schedule.  Note that
   * each expense will be tied to the equipment vendor's name.
   * 
   * ```json
   * "equipment": {
   *   "hasEquipment": "yes",
   *   "equipmentVendors": [{
   *     "name": "Some Vendor",
   *     "equipment": "Some Equipment"
   *     "currentBalance": 5000,
   *     "amount": 20000,
   *     "orderDate": "2021-09-25",
   *     "paymentDate": "2021-09-28"
   *   }]
   * }
   * ```
   */
   processEquipmentExpenses() {
    const {
      hasEquipment,
      equipmentVendors
    } = this.params.equipment;

    if (!equipmentVendors?.length || hasEquipment === 'no') {
      return;
    }

    equipmentVendors.forEach((record) => {
      this.buildExpenseSchedule(FIELD_EQUIPMENT_VENDORS, record);
    })
  }

  /**
   * Converts bond premium params in the following format into
   * expense line items within the master schedule.
   * 
   * ```json
   * "bondPremium": {
   *   "hasBondPremium": "yes",
   *   "totalAmount": 5000,
   *   "dueDate": "2021-09-30"
   * }
   * ```
   */
  processBondPremiumExpenses() {

    const {
      hasBondPremium,
      totalAmount,
      dueDate
    } = this.params.bondPremium;

    if (!dueDate || hasBondPremium === 'no') {
      return;
    }

    // Find the Friday the premium should fall into and apply it
    const dueDateFridayMoment = getNextFriday(dueDate);
    const date = dueDateFridayMoment.format('YYYY-MM-DD');
    this.schedule[date].bondPremium = totalAmount;
  }

  /**
   * Converts misc expense params in the following format into
   * expense line items within the master schedule.
   * 
   * ```json
   * "miscExpenses": {
   *   "hasMiscExpenses": "yes",
   *   "miscExpenses": [{
   *     "name": "Lunch",
   *     "amount": 5000,
   *     "paymentFrequency": "bi_weekly"
   *   }]
   * },
   * ```
   */
   processMiscExpenses() {
    const {
      hasMiscExpenses,
      miscExpenses
    } = this.params.miscExpenses;

    if (!miscExpenses?.length || hasMiscExpenses === 'no') {
      return;
    }

    miscExpenses.forEach((record) => {
      this.buildExpenseSchedule(FIELD_MISC_EXPENSES, record);
    });
  }

  /**
   * Builds out an expense schedule designed for use with 
   * expenses set to a frequency over the schedule with optional
   * payment terms
   * 
   * @param {string} section The section to apply the expense to
   * @param {object} params The function parameters
   * @param {string} params.name The label for the expense
   * @param {string} params.amountVariation The defined variation for the expense, fixed or variable
   * @param {string} params.paymentFrequency The defined frequency for the expense
   * @param {number} params.amount The interval amount for the expense
   * @param {number} params.payments Weekly variable payments for the expense
   * @param {number} params.paymentTerms The payment terms for the expenses
   */
  buildExpenseSchedule(section, { 
    name,
    amountVariation,
    paymentFrequency,
    amount,
    payments,
    paymentDate,
    paymentTerms = PAYMENT_TERM_COD
  }) {

    // Fixed payment logic
    if (paymentFrequency && amountVariation === 'fixed') {
      if (paymentFrequency === PAYMENT_FREQUENCY_ONE_TIME && paymentDate) {

        // Align the payment date with the term (if set)
        const termDate = getTermPaymentDate(paymentDate, paymentTerms);
  
        // Determine if there's already an expense on this date for the vendor
        const existingAmount = this.schedule[termDate][section][name] ?? 0;
  
        this.schedule[termDate][section][name] = existingAmount + amount;
  
      } else {
        const paymentIntervals = this.intervals[paymentFrequency];
  
        // Spread the payments out over each interval
        paymentIntervals.forEach(date => {
          // Align the payment date with the term (if set)
          const termDate = getTermPaymentDate(date, paymentTerms);
  
          // Determine if there's already an expense on this date for the vendor
          const existingAmount = this.schedule[termDate][section][name] ?? 0;

          this.schedule[termDate][section][name] = existingAmount + amount;
        });
      }
    } 
    
    if (amountVariation === 'variable') {
      payments.forEach(({ date, amount }) => {
        this.schedule[date][section][name] = amount;
      })
    }
  }

  /**
   * Flows retainage into the last day of the project
   */
  processRetainagePayment() {

    const {
      details: {
        totalValue,
        retainagePercent
      },
      payAppSchedule: {
        payAppAllocations
      }
    } = this.params;

    const retainagePayment = totalValue * (retainagePercent / 100);

    const lastPaymentDate = payAppAllocations[payAppAllocations.length - 1].date;

    this.schedule[lastPaymentDate].retainagePayment = retainagePayment;
  }

  /**
   * Computes weekly deficits based on allocated expenses
   */
  /* processCashFlowDeficit() {
    
    let previousWeek = null;
    this.schedule.forEach((week, index) => {
      if (index === 0) {
        
      }

    });
    cashFlow
    cumulativeCashFlow
  } */

  /**
   * Builds row heads for use in associated tables/spreadsheets
   */
  buildRowHeadings() {

    const { type } = this.params.type

    const map = {
      [TYPE_CONSTRUCTION]: this.getConstructionRowHeadings(),
      [TYPE_PO_MANUFACTURING]: this.getPoManufacturingRowHeadings(),
      [TYPE_PO_INVENTORY]: this.getPoInvoiceRowHeadings()
    }

    const rowHeadings = map[type];
    
    // Loop through every week in the schedule to find the children
    // of all applicable line items
    Object.values(this.schedule).forEach(scheduleWeekValues => {
  
      Object.entries(scheduleWeekValues).forEach(([lineItemKey, lineItemValues]) => {
  
        // If the line item is an object then we need to process 
        // all of its children
        if (typeof lineItemValues === 'object') {
          Object.keys(lineItemValues).forEach(childExpense => {
            if (!rowHeadings[lineItemKey].children.includes(childExpense)) {
              rowHeadings[lineItemKey].children.push(childExpense);
            }
          });
        }
      });
    });

    this.rowHeadings = rowHeadings;
  }

  /**
   * Retrieves row headings for construction flows
   * 
   * @returns {Object}
   */
  getConstructionRowHeadings() {
    return {
      income: {
        label: 'Week of Submitted Pay App',
        separator: true,
      },
      payApp: {
        label: 'Invoice or Pay App Submission',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_BLUE,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
      },
      retainagePercent: {
        label: '% of Retainage Held by your Customer',
        format: FORMAT_PERCENT,
        fillColor: COLOR_BLUE,
        color: COLOR_WHITE,
      },
      retainage: {
        label: '$ of Retainage Held by your Customer',
        format: FORMAT_CURRENCY,
        borderTop: BORDER_THIN,
        total: TOTAL_SUM,
      },
      totalIncome: {
        label: 'Amount to be Paid from Invoice or Pay App',
        format: FORMAT_CURRENCY,
        total: TOTAL_SUM,
        isTotalRow: true,
      },
      expenses: {
        label: 'Expenses by Week',
        separator: true,
      },
      payrollExpense: {
        label: 'Payroll',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_GREEN,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
      },
      subContractors: {
        label: 'Sub-Contractors',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_GREEN,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
        children: [],
      },
      materialOrders: {
        label: 'Materials',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_GREEN,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
        children: [],
      },
      equipmentVendors: {
        label: 'Equipment',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_GREEN,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
        children: [],
      },
      bondPremium: {
        label: 'Bond Premium',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_GREEN,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
      },
      miscExpenses: {
        label: 'Misc Expenses',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_GREEN,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
        children: [],
      },
      totalExpenses: {
        label: 'Total Expenses',
        format: FORMAT_CURRENCY,
        total: TOTAL_SUM,
        isTotalRow: true
      },
      cashPosition: {
        label: 'Cash Position of the Project',
        format: FORMAT_CURRENCY,
        separator: true,
      },
      payment: {
        label: 'Cash Received from Invoice or Pay App',
        format: FORMAT_CURRENCY,
      },
      cashFlow: {
        label: 'Weekly Cash Flow (Deficit / Surplus)',
        format: FORMAT_CURRENCY,
      },
      cumulativeCashFlow: {
        label: 'Cumulative Project Cash Flow (Deficit / Surplus)',
        format: FORMAT_CURRENCY,
        isTotalRow: true
      },
    };
  }

  /**
   * Retrieves row headings for PO - Manufacturing flows
   * 
   * @returns {Object}
   */
  getPoManufacturingRowHeadings() {
    return {
      income: {
        label: 'Week of Submitted Invoice',
        separator: true,
      },
      invoice: {
        label: 'Invoice Submission',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_BLUE,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
      },
      expenses: {
        label: 'Expenses by Week',
        separator: true,
      },
      payrollExpense: {
        label: 'Payroll',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_GREEN,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
      },
      subContractors: {
        label: 'Sub-Contractors',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_GREEN,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
        children: [],
      },
      materialOrders: {
        label: 'Materials',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_GREEN,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
        children: [],
      },
      equipmentVendors: {
        label: 'Equipment',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_GREEN,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
        children: [],
      },
      miscExpenses: {
        label: 'Misc Expenses',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_GREEN,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
        children: [],
      },
      totalExpenses: {
        label: 'Total Expenses',
        format: FORMAT_CURRENCY,
        total: TOTAL_SUM,
        isTotalRow: true
      },
      cashPosition: {
        label: 'Cash Position of the Project',
        format: FORMAT_CURRENCY,
        separator: true,
      },
      payment: {
        label: 'Cash Received from Invoice or Pay App',
        format: FORMAT_CURRENCY,
      },
      cashFlow: {
        label: 'Weekly Cash Flow (Deficit / Surplus)',
        format: FORMAT_CURRENCY,
      },
      cumulativeCashFlow: {
        label: 'Cumulative Project Cash Flow (Deficit / Surplus)',
        format: FORMAT_CURRENCY,
        isTotalRow: true
      },
    };
  }

  /**
   * Retrieves row headings for PO - Invoice flows
   * 
   * @returns {Object}
   */
  getPoInvoiceRowHeadings() {
    return {
      income: {
        label: 'Week of Submitted Invoice',
        separator: true,
      },
      invoice: {
        label: 'Invoice',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_BLUE,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
      },
      expenses: {
        label: 'Expenses',
        separator: true,
      },
      payment: {
        label: 'Payment',
        format: FORMAT_CURRENCY,
        fillColor: COLOR_GREEN,
        color: COLOR_WHITE,
        total: TOTAL_SUM,
      },
    }
  }
}

module.exports = CashFlow;