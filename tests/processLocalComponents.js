const CashFlow = require('../CashFlow');
const render = require('../render');
const ProjectionTable = require('../components/ProjectionTable');
const Summary = require('../components/Summary');
const path = require('path');
const fs = require('fs').promises;

const args = process.argv.slice(2);

(async () => {

  const inputPath = path.resolve(__dirname, '../../../events/', args[0]);
  const inputData = await fs.readFile(inputPath, 'utf8');

  const cashFlow = new CashFlow(JSON.parse(inputData))
  const result = cashFlow.process();

  const summaryHtml = render(Summary, result);
  const summaryFilePath = path.resolve(__dirname, 'summary.html');
  await fs.writeFile(summaryFilePath, summaryHtml);
  console.log('Summary HTML written to file');

  const tableHtml = render(ProjectionTable, result);
  const tableFilePath = path.resolve(__dirname, 'table.html');
  await fs.writeFile(tableFilePath, tableHtml);
  console.log('Table HTML written to file');
})();