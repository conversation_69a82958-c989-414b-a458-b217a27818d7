const getProcessorResults = require('../helpers/getProcessorResults');
const path = require('path');
const fs = require('fs').promises;

const args = process.argv.slice(2);

(async () => {
  const inputPath = path.resolve(__dirname, '../../../events/', args[0]);
  const inputData = await fs.readFile(inputPath, 'utf8');

  const processedData = getProcessorResults(JSON.parse(inputData));

  const json = JSON.stringify(processedData, null, 2);

  const filePath = path.resolve(__dirname, 'processed-data.json');
  await fs.writeFile(filePath, json);
  console.log('Data written to file');
})();
